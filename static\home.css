/* Home page specific styles */
body {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
}

.home-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.welcome-section {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    text-align: center;
}

.welcome-section h1 {
    color: #2d3436;
    font-size: 32px;
    margin-bottom: 15px;
}

.welcome-section h2 {
    color: #0984e3;
    font-size: 24px;
    margin-bottom: 20px;
}

.welcome-section p {
    color: #636e72;
    font-size: 16px;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 30px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons a {
    background-color: #0984e3;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-buttons a:hover {
    background-color: #74b9ff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.action-buttons .admin-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: 2px solid #dc3545;
    font-weight: bold;
}

.action-buttons .admin-btn:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    border-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

.action-buttons button {
    background-color: #e17055;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 16px;
}

.action-buttons button:hover {
    background-color: #d63031;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.user-info {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.user-info h3 {
    color: #2d3436;
    margin-bottom: 20px;
    font-size: 20px;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
}

.user-info p {
    color: #636e72;
    margin-bottom: 15px;
    line-height: 1.6;
}

.user-info strong {
    color: #2d3436;
}

/* Responsive design */
@media (max-width: 600px) {
    .home-container {
        padding: 20px 15px;
    }

    .welcome-section,
    .user-info {
        padding: 25px 20px;
    }

    .welcome-section h1 {
        font-size: 26px;
    }

    .welcome-section h2 {
        font-size: 20px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-buttons a,
    .action-buttons button {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }
}

/* Dialog styles */
.dialog-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dialog-overlay.show {
    opacity: 1;
}

.dialog-content {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    text-align: center;
    max-width: 420px;
    margin: 20px;
    transform: scale(0.8) translateY(30px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dialog-overlay.show .dialog-content {
    transform: scale(1) translateY(0);
}

.dialog-content h3 {
    color: #2d3436;
    font-size: 26px;
    margin-bottom: 8px;
    font-weight: 600;
}

.dialog-content p {
    color: #636e72;
    font-size: 16px;
    margin-bottom: 30px;
    line-height: 1.6;
    opacity: 0.9;
}

.dialog-content::before {
    content: "⚠️";
    font-size: 48px;
    display: block;
    margin-bottom: 20px;
    opacity: 0.8;
}

.dialog-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-danger {
    background: linear-gradient(135deg, #e17055, #d63031);
    color: white;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-danger::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-danger:hover::before {
    left: 100%;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d63031, #c0392b);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(225, 112, 85, 0.4);
}

.btn-cancel {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-cancel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-cancel:hover::before {
    left: 100%;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #0984e3, #0770c4);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
}

/* Responsive dialog */
@media (max-width: 500px) {
    .dialog-content {
        margin: 20px 15px;
        padding: 35px 25px;
        border-radius: 16px;
    }

    .dialog-content::before {
        font-size: 40px;
        margin-bottom: 15px;
    }

    .dialog-content h3 {
        font-size: 22px;
    }

    .dialog-actions {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .btn-danger,
    .btn-cancel {
        width: 100%;
        max-width: 220px;
        padding: 16px 24px;
    }
}

/* Additional polish */
.dialog-content {
    user-select: none;
}

.dialog-actions form {
    margin: 0;
    padding: 0;
    background: none;
    box-shadow: none;
}

/* Subtle pulse animation for warning icon */
@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.dialog-content::before {
    animation: pulse 2s ease-in-out infinite;
}