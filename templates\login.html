{% extends "base.html" %}

{% block title %}Connexion{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='login.css') }}">
{% endblock %}

{% block content %}
<div class="auth-page">
    <div class="auth-container">
        <div class="form-container">
            <!-- Professional Header -->
            <div class="form-header">
                <div class="logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1>Connexion</h1>
                <p>Accédez à votre espace éducatif</p>
            </div>

            <!-- Login Form -->
            <div class="form-body">
                <form method="POST" autocomplete="on" class="auth-form">
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i>
                            Adresse Email
                        </label>
                        <input type="email"
                               id="email"
                               name="email"
                               class="form-input"
                               placeholder="<EMAIL>"
                               required
                               autocomplete="username"
                               maxlength="100">
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i>
                            Mot de Passe
                        </label>
                        <div class="password-input-container">
                            <input type="password"
                                   id="password"
                                   name="password"
                                   class="form-input"
                                   placeholder="Entrez votre mot de passe"
                                   required
                                   autocomplete="current-password"
                                   maxlength="200"
                                   minlength="8">
                            <button type="button"
                                    class="password-toggle"
                                    onclick="togglePassword('password')"
                                    title="Afficher/Masquer le mot de passe">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full btn-lg">
                        <i class="fas fa-sign-in-alt"></i>
                        Se Connecter
                    </button>
                </form>

                <!-- Google Login -->
                <div class="auth-divider">
                    <span>ou</span>
                </div>

                <a href="{{ url_for('google') }}" class="google-auth-btn">
                    <div class="google-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                            <path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"/>
                            <path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"/>
                            <path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.17 0 20.26 0 24.55c0 4.29.92 8.38 2.56 11.81l7.97-6.19z"/>
                            <path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.49-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"/>
                        </svg>
                    </div>
                    <span>Continuer avec Google</span>
                </a>

                <!-- Signup Link -->
                <div class="auth-footer">
                    <p>Vous n'avez pas de compte ?</p>
                    <a href="{{ url_for('signup') }}" class="auth-link">
                        Créer un compte
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.parentElement.querySelector('.password-toggle');
    const icon = button.querySelector('i');

    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Enhanced form interactions
    const inputs = document.querySelectorAll('.form-input');

    inputs.forEach(input => {
        // Add focus effects
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value) {
                this.parentElement.classList.add('filled');
            } else {
                this.parentElement.classList.remove('filled');
            }
        });

        // Check if already filled on load
        if (input.value) {
            input.parentElement.classList.add('filled');
        }
    });

    // Auto-dismiss flash messages
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(function(message) {
        setTimeout(function() {
            message.style.transition = 'all 0.3s ease';
            message.style.transform = 'translateX(100%)';
            message.style.opacity = '0';

            setTimeout(function() {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 300);
        }, 5000);
    });

    // Google button ripple effect
    const googleBtn = document.querySelector('.google-auth-btn');
    if (googleBtn) {
        googleBtn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }
});
</script>
{% endblock %}