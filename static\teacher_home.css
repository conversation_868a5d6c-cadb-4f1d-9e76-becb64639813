/* Professional Teacher Dashboard Styles */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
}

/* Dashboard Layout */
.teacher-dashboard {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Professional Header */
.dashboard-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-section i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo-section h1 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* Navigation */
.main-nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover {
    background-color: #f1f5f9;
    color: var(--primary-color);
}

.nav-item.active {
    background-color: #eff6ff;
    color: var(--primary-color);
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.nav-item i {
    font-size: 1rem;
}

/* User Profile Section */
.header-right {
    display: flex;
    align-items: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn,
.logout-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.action-btn:hover {
    background-color: #f1f5f9;
    color: var(--primary-color);
}

.logout-btn:hover {
    background-color: #fef2f2;
    color: var(--danger-color);
}

/* Main Dashboard Content */
.dashboard-main {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1.5rem;
    width: 100%;
}

/* Content Sections */
.content-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-header {
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-header h2 i {
    color: var(--primary-color);
}

.section-header p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Quick Actions */
.quick-actions {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.quick-actions h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-actions h3 i {
    color: var(--warning-color);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: transparent;
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-secondary);
}

.action-card:hover {
    border-color: var(--primary-color);
    background: #eff6ff;
    color: var(--primary-color);
    transform: translateY(-2px);
}

.action-card i {
    font-size: 1.5rem;
}

.action-card span {
    font-weight: 500;
    font-size: 0.875rem;
}

/* Upload Card */
.upload-card {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.upload-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.upload-card h3 i {
    color: var(--primary-color);
}

/* Form Styles */
.upload-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group label i {
    color: var(--primary-color);
    font-size: 0.875rem;
}

.form-group select,
.form-group textarea {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: var(--surface-color);
    transition: all 0.2s ease;
    font-family: inherit;
}

.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* File Upload Area */
.file-upload-container {
    width: 100%;
}

.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    background: #fafbfc;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: #eff6ff;
}

.file-upload-area.drag-over {
    border-color: var(--success-color);
    background: #f0fdf4;
    transform: scale(1.02);
}

.upload-icon {
    margin-bottom: 1rem;
}

.upload-icon i {
    font-size: 3rem;
    color: var(--primary-color);
}

.file-upload-area h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.upload-subtext {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.file-requirements {
    margin-top: 1rem;
}

.file-requirements small {
    color: var(--text-muted);
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

#pdfFile {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* File Info Display */
.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #eff6ff;
    border-radius: var(--radius-lg);
    border: 1px solid #bfdbfe;
    margin-top: 1rem;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-details {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.file-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.file-text-content {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
}

.file-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    word-break: break-word;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.file-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--success-color);
}

.file-status i {
    font-size: 0.75rem;
}

.remove-file-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.remove-file-btn:hover {
    background: #b91c1c;
    transform: scale(1.1);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
}

.submit-btn,
.cancel-btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-decoration: none;
}

.submit-btn {
    background: var(--primary-color);
    color: white;
}

.submit-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.cancel-btn {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.cancel-btn:hover {
    background: #f8fafc;
    color: var(--text-primary);
}

/* Recent Documents */
.recent-documents {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.recent-documents h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.recent-documents h3 i {
    color: var(--secondary-color);
}

.documents-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.document-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.document-item:hover {
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

.doc-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: #fee2e2;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #dc2626;
    font-size: 1rem;
}

.doc-info {
    flex: 1;
    min-width: 0;
}

.doc-info h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.doc-info p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.doc-stats {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.doc-actions {
    display: flex;
    gap: 0.5rem;
}

.doc-actions .action-btn {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
}

/* Classes Grid */
.classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.class-card {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.class-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.class-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.class-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.student-count {
    background: #eff6ff;
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.class-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.class-stats .stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.class-stats .stat i {
    color: var(--primary-color);
}

.class-actions {
    display: flex;
    justify-content: flex-end;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.chart-card h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.chart-placeholder {
    height: 200px;
    background: #f8fafc;
    border-radius: var(--radius-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.chart-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.popular-docs {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.doc-rank {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: var(--radius-md);
}

.doc-rank .rank {
    width: 1.5rem;
    height: 1.5rem;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

.doc-rank .doc-name {
    flex: 1;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.doc-rank .downloads {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Profile Section */
.profile-section {
    position: fixed;
    top: 4rem;
    right: 1.5rem;
    z-index: 50;
}

.profile-card {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    min-width: 280px;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.profile-avatar {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.profile-info h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.profile-info p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.profile-role {
    background: #eff6ff;
    color: var(--primary-color);
    padding: 0.125rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.625rem;
    font-weight: 500;
}

.profile-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.profile-stat h4 {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.profile-stat p {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.status-active {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--success-color) !important;
}

.status-active i {
    font-size: 0.5rem;
}

/* Profile Menu */
.profile-menu {
    position: fixed;
    top: 4rem;
    right: 1.5rem;
    z-index: 200;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.profile-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.profile-menu-content {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    min-width: 250px;
    overflow: hidden;
}

.menu-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.menu-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.menu-info h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.125rem;
}

.menu-info p {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.menu-items {
    padding: 0.5rem 0;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.menu-item:hover {
    background: #f8fafc;
    color: var(--text-primary);
}

.menu-item i {
    width: 1rem;
    font-size: 0.875rem;
}

.menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.5rem 0;
}

/* Professional Dialog Styles */
.dialog-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dialog-overlay.show {
    opacity: 1;
}

.dialog-content {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    text-align: center;
    max-width: 400px;
    margin: 1rem;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid var(--border-color);
}

.dialog-overlay.show .dialog-content {
    transform: scale(1) translateY(0);
}

.dialog-icon {
    width: 4rem;
    height: 4rem;
    background: #fef2f2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: var(--danger-color);
    font-size: 1.5rem;
}

.dialog-content h3 {
    color: var(--text-primary);
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.dialog-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.dialog-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-danger:hover {
    background: #b91c1c;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-cancel {
    background: transparent;
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-cancel:hover {
    background: #f8fafc;
    color: var(--text-primary);
    border-color: var(--text-secondary);
}

/* Notification System */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    min-width: 300px;
    max-width: 400px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.info {
    border-left: 4px solid var(--primary-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
}

.notification-content i {
    font-size: 1rem;
    color: var(--primary-color);
}

.notification.error .notification-content i {
    color: var(--danger-color);
}

.notification.success .notification-content i {
    color: var(--success-color);
}

.notification-content span {
    flex: 1;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    font-size: 0.75rem;
    transition: color 0.2s ease;
}

.notification-close:hover {
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .header-container {
        padding: 0 1rem;
    }

    .dashboard-main {
        padding: 1.5rem 1rem;
    }
}

@media (max-width: 768px) {
    .header-container {
        height: auto;
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-left {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .main-nav {
        justify-content: space-between;
        overflow-x: auto;
        padding: 0.5rem 0;
    }

    .nav-item {
        flex-shrink: 0;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .nav-item span {
        display: none;
    }

    .nav-item.active::after {
        display: none;
    }

    .user-profile {
        justify-content: space-between;
        width: 100%;
    }

    .user-details {
        align-items: flex-start;
    }

    .dashboard-main {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .file-upload-area {
        padding: 2rem 1rem;
    }

    .upload-icon i {
        font-size: 2rem;
    }

    .file-upload-area h4 {
        font-size: 1rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .submit-btn,
    .cancel-btn {
        width: 100%;
        justify-content: center;
    }

    .classes-grid {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .profile-section {
        position: static;
        margin-top: 2rem;
    }

    .profile-menu {
        position: fixed;
        top: auto;
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
    }

    .notification {
        left: 1rem;
        right: 1rem;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        position: static;
    }

    .header-container {
        padding: 0.75rem;
    }

    .logo-section h1 {
        font-size: 1.125rem;
    }

    .nav-item {
        padding: 0.5rem;
    }

    .user-avatar {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }

    .user-name {
        font-size: 0.75rem;
    }

    .user-role {
        font-size: 0.625rem;
    }

    .dashboard-main {
        padding: 0.75rem;
    }

    .section-header h2 {
        font-size: 1.25rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .upload-card,
    .recent-documents,
    .quick-actions {
        padding: 1rem;
    }

    .file-upload-area {
        padding: 1.5rem 0.75rem;
    }

    .dialog-content {
        margin: 0.75rem;
        padding: 1.5rem;
    }

    .dialog-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-danger,
    .btn-cancel {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .dashboard-header,
    .user-actions,
    .dialog-overlay,
    .notification,
    .profile-menu {
        display: none !important;
    }

    .dashboard-main {
        padding: 0;
        max-width: none;
    }

    .content-section {
        display: block !important;
    }

    .stats-grid,
    .classes-grid,
    .analytics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #0f172a;
        --surface-color: #1e293b;
        --border-color: #334155;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #64748b;
    }
}