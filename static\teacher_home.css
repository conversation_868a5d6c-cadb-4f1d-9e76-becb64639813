/* Base styles */
body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
.header {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 24px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-name {
    font-weight: 500;
    color: #34495e;
}

.logout-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.2s;
}

.logout-btn:hover {
    background: #c0392b;
}

/* Main content */
.main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

/* Upload card */
.upload-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 28px;
}

.upload-card h2 {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #34495e;
    font-size: 14px;
}

.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 15px;
    background: #fafafa;
    transition: border-color 0.2s;
}

.form-group select:focus,
.form-group textarea:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Redesigned File Upload */
.file-upload-container {
    width: 100%;
}

.file-upload-area {
    border: 2px dashed #3498db;
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    background-color: #f8fbff;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.file-upload-area:hover {
    border-color: #2980b9;
    background-color: #edf7ff;
}

.file-upload-area.drag-over {
    border-color: #27ae60;
    background-color: #e8f7f0;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    color: #3498db;
}

.upload-text {
    font-size: 16px;
    color: #2c3e50;
    margin: 0 0 10px;
    font-weight: 500;
}

.upload-subtext {
    color: #7f8c8d;
    margin: 5px 0;
    font-size: 14px;
}

.browse-btn {
    background: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 10px;
}

.browse-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.25);
}

#pdfFile {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* File Info Display */
.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #e8f4ff;
    border-radius: 12px;
    border: 1px solid #b3d9ff;
    margin-top: 15px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.file-icon {
    font-size: 24px;
    color: #3498db;
}

.file-text-content {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.file-name {
    font-size: 15px;
    font-weight: 500;
    color: #2c3e50;
    word-break: break-word;
    margin-bottom: 3px;
}

.file-size {
    font-size: 13px;
    color: #7f8c8d;
}

.remove-file-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.remove-file-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.remove-icon {
    font-size: 18px;
    font-weight: bold;
}

/* Drag and drop feedback */
.file-upload-area.drag-over::before {
    content: "Déposez le fichier ici";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    font-weight: 500;
    color: #27ae60;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 8px;
    pointer-events: none;
}

/* Submit button */
.submit-btn {
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 14px 20px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    transition: background 0.2s;
}

.submit-btn:hover {
    background: #219653;
}

/* Info section */
.info-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.info-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 24px;
}

.info-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #7f8c8d;
}

.info-value {
    padding-left: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group textarea {
    width: 100%;
    max-width: 100%;
    min-width: 100%;
    height: 120px;
    min-height: 80px;
    max-height: 300px;
    box-sizing: border-box;
}

/* Original Dialog styles (preserved) */
.dialog-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dialog-overlay.show {
    opacity: 1;
}

.dialog-content {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    text-align: center;
    max-width: 420px;
    margin: 20px;
    transform: scale(0.8) translateY(30px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dialog-overlay.show .dialog-content {
    transform: scale(1) translateY(0);
}

.dialog-content h3 {
    color: #2d3436;
    font-size: 26px;
    margin-bottom: 8px;
    font-weight: 600;
}

.dialog-content p {
    color: #636e72;
    font-size: 16px;
    margin-bottom: 30px;
    line-height: 1.6;
    opacity: 0.9;
}

.dialog-content::before {
    content: "⚠️";
    font-size: 48px;
    display: block;
    margin-bottom: 20px;
    opacity: 0.8;
}

.dialog-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-danger {
    background: linear-gradient(135deg, #e17055, #d63031);
    color: white;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-danger::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-danger:hover::before {
    left: 100%;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d63031, #c0392b);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(225, 112, 85, 0.4);
}

.btn-cancel {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-cancel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-cancel:hover::before {
    left: 100%;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #0984e3, #0770c4);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
}

/* Responsive dialog */
@media (max-width: 500px) {
    .dialog-content {
        margin: 20px 15px;
        padding: 35px 25px;
        border-radius: 16px;
    }

    .dialog-content::before {
        font-size: 40px;
        margin-bottom: 15px;
    }

    .dialog-content h3 {
        font-size: 22px;
    }

    .dialog-actions {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .btn-danger,
    .btn-cancel {
        width: 100%;
        max-width: 220px;
        padding: 16px 24px;
    }
}

/* Additional polish */
.dialog-content {
    user-select: none;
}

.dialog-actions form {
    margin: 0;
    padding: 0;
    background: none;
    box-shadow: none;
}

/* Subtle pulse animation for warning icon */
@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.dialog-content::before {
    animation: pulse 2s ease-in-out infinite;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .main-content {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .file-upload-area {
        padding: 25px 15px;
    }

    .upload-icon {
        font-size: 40px;
    }

    .upload-text {
        font-size: 15px;
    }

    .file-info {
        padding: 12px 15px;
    }

    .file-details {
        gap: 12px;
    }

    .file-name {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .upload-card {
        padding: 20px;
    }

    .header {
        padding: 16px;
    }

    .header h1 {
        font-size: 20px;
    }

    .browse-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
}