{% extends "base.html" %}

{% block title %}Espace Enseignant - Plateforme Éducative{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='teacher_home.css') }}">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="teacher-dashboard">
    <!-- Professional Header with Navigation -->
    <header class="dashboard-header">
        <div class="header-container">
            <div class="header-left">
                <div class="logo-section">
                    <i class="fas fa-graduation-cap"></i>
                    <h1>Espace Enseignant</h1>
                </div>
                <nav class="main-nav">
                    <a href="#dashboard" class="nav-item active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Tableau de bord</span>
                    </a>
                    <a href="#documents" class="nav-item" data-section="documents">
                        <i class="fas fa-file-pdf"></i>
                        <span>Documents</span>
                    </a>
                    <a href="#classes" class="nav-item" data-section="classes">
                        <i class="fas fa-users"></i>
                        <span>Classes</span>
                    </a>
                    <a href="#analytics" class="nav-item" data-section="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>Statistiques</span>
                    </a>
                </nav>
            </div>
            <div class="header-right">
                <div class="user-profile">
                    <div class="user-avatar">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name">{{ user.name }}</span>
                        <span class="user-role">Enseignant</span>
                    </div>
                    <div class="user-actions">
                        <button class="action-btn" onclick="showProfileMenu()" title="Profil">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="logout-btn" onclick="showLogoutDialog()" title="Déconnexion">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard Content -->
    <main class="dashboard-main">
        <!-- Dashboard Overview Section -->
        <section id="dashboard-section" class="content-section active">
            <div class="section-header">
                <h2><i class="fas fa-tachometer-alt"></i> Tableau de bord</h2>
                <p>Vue d'ensemble de votre activité d'enseignement</p>
            </div>

            <!-- Quick Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="stat-content">
                        <h3>12</h3>
                        <p>Documents partagés</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>156</h3>
                        <p>Étudiants actifs</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-content">
                        <h3>342</h3>
                        <p>Téléchargements</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Cette semaine</h3>
                        <p>Dernière activité</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3><i class="fas fa-bolt"></i> Actions rapides</h3>
                <div class="action-buttons">
                    <button class="action-card" onclick="switchToSection('documents')">
                        <i class="fas fa-plus-circle"></i>
                        <span>Ajouter un document</span>
                    </button>
                    <button class="action-card" onclick="switchToSection('classes')">
                        <i class="fas fa-eye"></i>
                        <span>Voir mes classes</span>
                    </button>
                    <button class="action-card" onclick="switchToSection('analytics')">
                        <i class="fas fa-chart-line"></i>
                        <span>Consulter les stats</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- Documents Management Section -->
        <section id="documents-section" class="content-section">
            <div class="section-header">
                <h2><i class="fas fa-file-pdf"></i> Gestion des documents</h2>
                <p>Ajoutez et gérez vos ressources pédagogiques</p>
            </div>

            <div class="upload-card">
                <h3><i class="fas fa-cloud-upload-alt"></i> Ajouter un nouveau document</h3>
                <form id="pdfUploadForm" method="POST" action="{{ url_for('teacher_upload') }}" enctype="multipart/form-data" class="upload-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="classSelect">
                                <i class="fas fa-graduation-cap"></i>
                                Classe
                            </label>
                            <select id="classSelect" name="class_id" required>
                                <option value="">Sélectionner une classe</option>
                                <option value="1">1er</option>
                                <option value="2">2éme</option>
                                <option value="3">3éme</option>
                                <option value="4">Baccalauréat</option>
                            </select>
                        </div>

                        <div class="form-group" id="sectionGroup">
                            <label for="subjectSelect">
                                <i class="fas fa-book"></i>
                                Section
                            </label>
                            <select id="subjectSelect" name="subject_id">
                                <option value="">Sélectionner une Section</option>
                                <option value="1">Informatique</option>
                                <option value="2">Mathématiques</option>
                                <option value="3">Sciences expérimentales</option>
                                <option value="4">Economie & gestion</option>
                                <option value="5">Technique</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="pdfFile">
                            <i class="fas fa-file-pdf"></i>
                            Document PDF
                        </label>
                        <div class="file-upload-container">
                            <div class="file-upload-area" id="dropZone">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h4>Glissez-déposez votre fichier ici</h4>
                                <p class="upload-subtext">ou cliquez pour parcourir</p>
                                <div class="file-requirements">
                                    <small><i class="fas fa-info-circle"></i> Format PDF uniquement, taille max: 10MB</small>
                                </div>
                                <input type="file" id="pdfFile" name="pdf_file" accept=".pdf" required>
                            </div>

                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="file-details">
                                    <div class="file-icon">
                                        <i class="fas fa-file-pdf"></i>
                                    </div>
                                    <div class="file-text-content">
                                        <span id="fileName" class="file-name"></span>
                                        <span id="fileSize" class="file-size"></span>
                                        <div class="file-status">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Prêt à être téléchargé</span>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="remove-file-btn" onclick="removeFile()" title="Supprimer le fichier">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">
                            <i class="fas fa-align-left"></i>
                            Description du document
                        </label>
                        <textarea id="description" name="description" rows="4"
                            placeholder="Décrivez le contenu du document, les objectifs pédagogiques, le niveau requis..."></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-upload"></i>
                            <span>Publier le document</span>
                        </button>
                        <button type="button" class="cancel-btn" onclick="resetForm()">
                            <i class="fas fa-times"></i>
                            <span>Annuler</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Recent Documents -->
            <div class="recent-documents">
                <h3><i class="fas fa-history"></i> Documents récents</h3>
                <div class="documents-list">
                    <div class="document-item">
                        <div class="doc-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="doc-info">
                            <h4>Cours de Mathématiques - Chapitre 5</h4>
                            <p>Publié le 15 Nov 2024 • 2éme Mathématiques</p>
                            <span class="doc-stats">
                                <i class="fas fa-download"></i> 23 téléchargements
                            </span>
                        </div>
                        <div class="doc-actions">
                            <button class="action-btn" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="document-item">
                        <div class="doc-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="doc-info">
                            <h4>Exercices d'Informatique</h4>
                            <p>Publié le 12 Nov 2024 • 3éme Informatique</p>
                            <span class="doc-stats">
                                <i class="fas fa-download"></i> 45 téléchargements
                            </span>
                        </div>
                        <div class="doc-actions">
                            <button class="action-btn" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Classes Management Section -->
        <section id="classes-section" class="content-section">
            <div class="section-header">
                <h2><i class="fas fa-users"></i> Mes Classes</h2>
                <p>Gérez vos classes et suivez l'activité de vos étudiants</p>
            </div>

            <div class="classes-grid">
                <div class="class-card">
                    <div class="class-header">
                        <h3>2éme Mathématiques</h3>
                        <span class="student-count">28 étudiants</span>
                    </div>
                    <div class="class-stats">
                        <div class="stat">
                            <i class="fas fa-file-pdf"></i>
                            <span>5 documents</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-download"></i>
                            <span>142 téléchargements</span>
                        </div>
                    </div>
                    <div class="class-actions">
                        <button class="btn-primary">
                            <i class="fas fa-eye"></i>
                            Voir la classe
                        </button>
                    </div>
                </div>

                <div class="class-card">
                    <div class="class-header">
                        <h3>3éme Informatique</h3>
                        <span class="student-count">32 étudiants</span>
                    </div>
                    <div class="class-stats">
                        <div class="stat">
                            <i class="fas fa-file-pdf"></i>
                            <span>8 documents</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-download"></i>
                            <span>256 téléchargements</span>
                        </div>
                    </div>
                    <div class="class-actions">
                        <button class="btn-primary">
                            <i class="fas fa-eye"></i>
                            Voir la classe
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Analytics Section -->
        <section id="analytics-section" class="content-section">
            <div class="section-header">
                <h2><i class="fas fa-chart-bar"></i> Statistiques</h2>
                <p>Analysez l'engagement et l'activité de vos étudiants</p>
            </div>

            <div class="analytics-grid">
                <div class="chart-card">
                    <h3>Téléchargements par semaine</h3>
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-line"></i>
                        <p>Graphique des téléchargements</p>
                    </div>
                </div>

                <div class="chart-card">
                    <h3>Documents les plus populaires</h3>
                    <div class="popular-docs">
                        <div class="doc-rank">
                            <span class="rank">1</span>
                            <span class="doc-name">Cours Mathématiques Ch.5</span>
                            <span class="downloads">45 téléchargements</span>
                        </div>
                        <div class="doc-rank">
                            <span class="rank">2</span>
                            <span class="doc-name">Exercices Informatique</span>
                            <span class="downloads">38 téléchargements</span>
                        </div>
                        <div class="doc-rank">
                            <span class="rank">3</span>
                            <span class="doc-name">TP Sciences</span>
                            <span class="downloads">29 téléchargements</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Profile Section -->
        <section class="profile-section">
            <div class="profile-card">
                <div class="profile-header">
                    <div class="profile-avatar">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="profile-info">
                        <h3>{{ user.name }}</h3>
                        <p>{{ user.email }}</p>
                        <span class="profile-role">Enseignant</span>
                    </div>
                </div>
                <div class="profile-stats">
                    <div class="profile-stat">
                        <h4>Membre depuis</h4>
                        <p>{{ user.created_at.strftime('%B %Y') }}</p>
                    </div>
                    <div class="profile-stat">
                        <h4>Statut</h4>
                        <p class="status-active">
                            <i class="fas fa-circle"></i>
                            Actif
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>
</div>

<!-- Professional Logout Dialog -->
<div id="logoutDialog" class="dialog-overlay">
    <div class="dialog-content">
        <div class="dialog-icon">
            <i class="fas fa-sign-out-alt"></i>
        </div>
        <h3>Confirmer la déconnexion</h3>
        <p>Êtes-vous sûr de vouloir vous déconnecter de votre espace enseignant ?</p>
        <div class="dialog-actions">
            <form method="POST" action="{{ url_for('logout') }}" style="display: inline;">
                <button type="submit" class="btn-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    Oui, me déconnecter
                </button>
            </form>
            <button type="button" class="btn-cancel" onclick="hideLogoutDialog()">
                <i class="fas fa-times"></i>
                Annuler
            </button>
        </div>
    </div>
</div>

<!-- Profile Menu -->
<div id="profileMenu" class="profile-menu">
    <div class="profile-menu-content">
        <div class="menu-header">
            <div class="menu-avatar">
                <i class="fas fa-user-tie"></i>
            </div>
            <div class="menu-info">
                <h4>{{ user.name }}</h4>
                <p>{{ user.email }}</p>
            </div>
        </div>
        <div class="menu-items">
            <a href="#" class="menu-item">
                <i class="fas fa-user-edit"></i>
                <span>Modifier le profil</span>
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-question-circle"></i>
                <span>Aide</span>
            </a>
            <div class="menu-divider"></div>
            <a href="#" class="menu-item" onclick="showLogoutDialog()">
                <i class="fas fa-sign-out-alt"></i>
                <span>Déconnexion</span>
            </a>
        </div>
    </div>
</div>

<script>
    // Navigation and Section Management
    document.addEventListener('DOMContentLoaded', function () {
        initializeNavigation();
        initializeFileUpload();
        initializeInteractions();
    });

    function initializeNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        const sections = document.querySelectorAll('.content-section');

        navItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const targetSection = this.dataset.section;
                switchToSection(targetSection);

                // Update active nav item
                navItems.forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    }

    function switchToSection(sectionName) {
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        const targetSection = document.getElementById(sectionName + '-section');
        if (targetSection) {
            targetSection.classList.add('active');
        }
    }

    function initializeFileUpload() {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('pdfFile');

        if (!dropZone || !fileInput) return;

        // Click on drop zone triggers file input
        dropZone.addEventListener('click', function (e) {
            if (e.target !== fileInput) {
                fileInput.click();
            }
        });

        // Drag and drop events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('drag-over');
        }

        function unhighlight() {
            dropZone.classList.remove('drag-over');
        }

        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        // Handle selected files
        fileInput.addEventListener('change', function () {
            handleFiles(this.files);
        });

        function handleFiles(files) {
            if (files.length > 0) {
                const file = files[0];
                if (file.type === 'application/pdf' || file.name.endsWith('.pdf')) {
                    displayFileInfo(file);
                } else {
                    showNotification('Veuillez sélectionner un fichier PDF valide.', 'error');
                    removeFile();
                }
            }
        }

        function displayFileInfo(file) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileInfo').style.display = 'flex';
            dropZone.style.display = 'none';
        }
    }

    function initializeInteractions() {
        // Initialize class/subject visibility
        const classSelect = document.getElementById('classSelect');
        if (classSelect) {
            classSelect.addEventListener('change', function () {
                updateSubjectVisibility(this.value);
            });
            updateSubjectVisibility(classSelect.value);
        }

        // Initialize tooltips and animations
        initializeTooltips();

        // Close profile menu when clicking outside
        document.addEventListener('click', function(e) {
            const profileMenu = document.getElementById('profileMenu');
            const profileButton = document.querySelector('.action-btn');

            if (profileMenu && !profileMenu.contains(e.target) && !profileButton.contains(e.target)) {
                profileMenu.classList.remove('show');
            }
        });
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function removeFile() {
        const fileInput = document.getElementById('pdfFile');
        const fileInfo = document.getElementById('fileInfo');
        const dropZone = document.getElementById('dropZone');

        if (fileInput) fileInput.value = '';
        if (fileInfo) fileInfo.style.display = 'none';
        if (dropZone) dropZone.style.display = 'block';
    }

    function resetForm() {
        const form = document.getElementById('pdfUploadForm');
        if (form) {
            form.reset();
            removeFile();
            updateSubjectVisibility('');
        }
    }

    // Profile Menu Functions
    function showProfileMenu() {
        const profileMenu = document.getElementById('profileMenu');
        if (profileMenu) {
            profileMenu.classList.toggle('show');
        }
    }

    // Dialog Functions
    function showLogoutDialog() {
        const dialog = document.getElementById('logoutDialog');
        if (dialog) {
            dialog.style.display = 'flex';
            setTimeout(() => {
                dialog.classList.add('show');
            }, 10);
            const firstButton = dialog.querySelector('.btn-danger');
            if (firstButton) firstButton.focus();
        }
    }

    function hideLogoutDialog() {
        const dialog = document.getElementById('logoutDialog');
        if (dialog) {
            dialog.classList.remove('show');
            setTimeout(() => {
                dialog.style.display = 'none';
            }, 300);
        }
    }

    // Notification System
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    function initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[title]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                // Add tooltip functionality if needed
            });
        });
    }

    function updateSubjectVisibility(classId) {
        const subjectSelect = document.getElementById('subjectSelect');
        const subjectGroup = subjectSelect?.closest('.form-group');

        if (!subjectSelect || !subjectGroup) return;

        // Handle 1er case - hide entire subject section
        if (classId === '1') {
            subjectGroup.style.display = 'none';
            subjectSelect.value = '';
            subjectSelect.removeAttribute('required');
            return;
        }

        // Show subject section for other classes
        subjectGroup.style.display = 'block';
        subjectSelect.setAttribute('required', 'required');

        // Handle 2éme case - hide technique option
        const options = subjectSelect.querySelectorAll('option[value]:not([value=""])');
        options.forEach(option => {
            if (classId === '2' && option.value === '5') {
                option.style.display = 'none';
            } else {
                option.style.display = 'block';
            }
        });

        // Reset selection if currently selected option is now hidden
        const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
        if (selectedOption && selectedOption.style.display === 'none') {
            subjectSelect.value = '';
        }
    }

    // Event Listeners
    document.addEventListener('DOMContentLoaded', function () {
        // Logout dialog events
        const logoutDialog = document.getElementById('logoutDialog');
        if (logoutDialog) {
            logoutDialog.addEventListener('click', function (e) {
                if (e.target === this) {
                    hideLogoutDialog();
                }
            });

            logoutDialog.addEventListener('keydown', function (e) {
                if (e.key === 'Tab') {
                    const focusableElements = this.querySelectorAll('button');
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];

                    if (e.shiftKey && document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    } else if (!e.shiftKey && document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            });
        }

        // Global keyboard shortcuts
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') {
                hideLogoutDialog();
                const profileMenu = document.getElementById('profileMenu');
                if (profileMenu) profileMenu.classList.remove('show');
            }
        });
    });
</script>
{% endblock %}