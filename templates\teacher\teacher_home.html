{% extends "base.html" %}

{% block title %}Espace Enseignant{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='teacher_home.css') }}">
{% endblock %}

{% block content %}
<div class="container">
    <header class="header">
        <div class="header-content">
            <h1>Espace Enseignant</h1>
            <div class="user-info">
                <span class="user-name">{{ user.name }}</span>
                <button class="logout-btn" onclick="showLogoutDialog()">
                    <i class="icon">🚪</i> Déconnexion
                </button>
            </div>
        </div>
    </header>

    <main class="main-content">
        <section class="upload-card">
            <h2>Ajouter un document PDF</h2>
            <form id="pdfUploadForm" method="POST" action="./upload" enctype="multipart/form-data">
                <div class="form-row">
                    <div class="form-group">
                        <label for="classSelect">Classe</label>
                        <select id="classSelect" name="class_id" required>
                            <option value="">Sélectionner une classe</option>
                            <option value="1">1er</option>
                            <option value="2">2éme</option>
                            <option value="3">3éme</option>
                            <option value="4">Baccalauréat</option>
                        </select>
                    </div>

                    <div class="form-group" id="sectionGroup">
                        <label for="subjectSelect">Section</label>
                        <select id="subjectSelect" name="subject_id">
                            <option value="">Sélectionner une Section</option>
                            <option value="1">Informatique</option>
                            <option value="2">Mathématiques</option>
                            <option value="3">Sciences expérimentales</option>
                            <option value="4">Economie & gestion</option>
                            <option value="5">Technique</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="pdfFile">Fichier PDF</label>
                    <div class="file-upload-container">
                        <div class="file-upload-area" id="dropZone">
                            <div class="upload-icon">📄</div>
                            <p class="upload-text">Glissez-déposez votre fichier ici</p>
                            <p class="upload-subtext">ou</p>
                            <button type="button" class="browse-btn"
                                onclick="document.getElementById('pdfFile').click()">
                                Parcourir les fichiers
                            </button>
                            <input type="file" id="pdfFile" name="pdf_file" accept=".pdf" required>
                        </div>

                        <div id="fileInfo" class="file-info" style="display: none;">
                            <div class="file-details">
                                <span class="file-icon">📄</span>
                                <div class="file-text-content">
                                    <span id="fileName" class="file-name"></span>
                                    <span id="fileSize" class="file-size"></span>
                                </div>
                            </div>
                            <button type="button" class="remove-file-btn" onclick="removeFile()">
                                <span class="remove-icon">✕</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Description (optionnel)</label>
                    <textarea id="description" name="description" rows="3"
                        placeholder="Décrivez le contenu du document..."></textarea>
                </div>

                <button type="submit" class="submit-btn">
                    <i class="icon">📤</i> Ajouter le document
                </button>
            </form>
        </section>

        <section class="info-section">
            <div class="info-card">
                <h3>Informations du compte</h3>
                <div class="info-item">
                    <span class="info-label">Nom:</span>
                    <span class="info-value">{{ user.name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ user.email }}</span>
                </div>
            </div>
        </section>
    </main>
</div>

<!-- Logout Confirmation Dialog (Original Design) -->
<div id="logoutDialog" class="dialog-overlay">
    <div class="dialog-content">
        <h3>Confirmer la déconnexion</h3>
        <p>Êtes-vous sûr de vouloir vous déconnecter ?</p>
        <div class="dialog-actions">
            <form method="POST" action="{{ url_for('logout') }}" style="display: inline;">
                <button type="submit" class="btn-danger">Oui, me déconnecter</button>
            </form>
            <button type="button" class="btn-cancel" onclick="hideLogoutDialog()">Annuler</button>
        </div>
    </div>
</div>

<script>
    // Enhanced file handling with drag and drop
    document.addEventListener('DOMContentLoaded', function () {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('pdfFile');

        // Click on drop zone triggers file input
        dropZone.addEventListener('click', function (e) {
            if (e.target !== fileInput) {
                fileInput.click();
            }
        });

        // Drag and drop events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('drag-over');
        }

        function unhighlight() {
            dropZone.classList.remove('drag-over');
        }

        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        // Handle selected files
        fileInput.addEventListener('change', function () {
            handleFiles(this.files);
        });

        function handleFiles(files) {
            if (files.length > 0) {
                const file = files[0];
                if (file.type === 'application/pdf' || file.name.endsWith('.pdf')) {
                    displayFileInfo(file);
                } else {
                    alert('Veuillez sélectionner un fichier PDF valide.');
                    removeFile();
                }
            }
        }

        function displayFileInfo(file) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileInfo').style.display = 'flex';
            dropZone.style.display = 'none';
        }
    });

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function removeFile() {
        const fileInput = document.getElementById('pdfFile');
        const fileInfo = document.getElementById('fileInfo');
        const dropZone = document.getElementById('dropZone');

        fileInput.value = '';
        fileInfo.style.display = 'none';
        dropZone.style.display = 'block';
    }

    // Existing functions remain unchanged
    function showLogoutDialog() {
        const dialog = document.getElementById('logoutDialog');
        dialog.style.display = 'flex';
        setTimeout(() => {
            dialog.classList.add('show');
        }, 10);
        const firstButton = dialog.querySelector('.btn-danger');
        firstButton.focus();
    }

    function hideLogoutDialog() {
        const dialog = document.getElementById('logoutDialog');
        dialog.classList.remove('show');
        setTimeout(() => {
            dialog.style.display = 'none';
        }, 300);
    }

    document.getElementById('logoutDialog').addEventListener('click', function (e) {
        if (e.target === this) {
            hideLogoutDialog();
        }
    });

    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            hideLogoutDialog();
        }
    });

    document.getElementById('logoutDialog').addEventListener('keydown', function (e) {
        if (e.key === 'Tab') {
            const focusableElements = this.querySelectorAll('button');
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];

            if (e.shiftKey && document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    });

    function updateSubjectVisibility(classId) {
        const subjectSelect = document.getElementById('subjectSelect');
        const subjectGroup = subjectSelect.closest('.form-group');

        // Handle 1er case - hide entire subject section
        if (classId === '1') {
            subjectGroup.style.display = 'none';
            subjectSelect.value = '';
            subjectSelect.removeAttribute('required');
            return;
        }

        // Show subject section for other classes
        subjectGroup.style.display = 'block';
        subjectSelect.setAttribute('required', 'required');

        // Handle 2éme case - hide technique option
        const options = subjectSelect.querySelectorAll('option[value]:not([value=""])');
        options.forEach(option => {
            if (classId === '2' && option.value === '5') {
                // Hide technique for 2éme
                option.style.display = 'none';
            } else {
                // Show all other options
                option.style.display = 'block';
            }
        });

        // Reset selection if currently selected option is now hidden
        const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
        if (selectedOption && selectedOption.style.display === 'none') {
            subjectSelect.value = '';
        }
    }

    // Add event listener
    document.getElementById('classSelect').addEventListener('change', function () {
        updateSubjectVisibility(this.value);
    });

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function () {
        const classSelect = document.getElementById('classSelect');
        updateSubjectVisibility(classSelect.value);
    });
</script>
{% endblock %}