import os
from flask import Flask, render_template, request, redirect, url_for, flash, session, make_response
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from models import db, User , Admin , Teacher , Student
from admin_utils import admin_required, create_admin_session, destroy_admin_session, generate_csrf_token, verify_csrf_token, rate_limit_check
from authlib.integrations.flask_client import OAuth
import logging
from dotenv import load_dotenv
load_dotenv()


classes = {1: "1er", 2: "2éme", 3: "3éme", 4: "Baccalauréat"}
subjects = {1: "Informatique", 2: "Mathématiques", 3: "Sciences expérimentales", 
           4: "Economie & gestion", 5: "Technique"}

app = Flask(__name__)

# Test Git
# Reduce werkzeug logging
# log = logging.getLogger('werkzeug')
# log.setLevel(logging.WARNING)

# Create Flask app
app = Flask(__name__)
print("#"*40)
print("#"*40)
print("initialisation de l'application : ")
print()
# Simple configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///app.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db.init_app(app)

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'
login_manager.login_message_category = 'info'

#google oauth
oauth = OAuth(app)
google = oauth.register(
    name='google',
    client_id=os.environ.get('GOOGLE_CLIENT_ID'),
    client_secret=os.environ.get('GOOGLE_CLIENT_SECRET'),
    server_metadata_url='https://accounts.google.com/.well-known/openid-configuration',
    client_kwargs={
        'scope': 'openid email profile'
    }
)
@app.route('/authorize')
def authorize():
    try:
        google = oauth.create_client('google')
        token = google.authorize_access_token()
        
        # Get nonce from session
        nonce = session.pop('oauth_nonce', None)
        
        # Parse ID token with nonce
        user_info = google.parse_id_token(token, nonce=nonce)
        
        # Check if user already exists
        email = user_info.get('email')
        user = User.query.filter_by(email=email).first()
        print(user)
        if not user:
            # Create new user if doesn't exist
            user = User(
                first_name=user_info.get('given_name', ''),
                last_name=user_info.get('family_name', ''),
                email=email,
                is_teacher=False
            )
            # Set a random password for OAuth users (they won't use it for login)
            import secrets
            random_password = secrets.token_urlsafe(32)
            user.set_password(random_password)
            db.session.add(user)
            db.session.commit()
            flash('Compte créé avec succès via Google!', 'success')
        else:
            flash('Connexion réussie via Google!', 'success')
        
        # Log in the user
        login_user(user)
        
        if user.is_teacher():
            return redirect(url_for('teacher_home'))
        else:
            return redirect(url_for('home'))
            
    except Exception as e:
        print(f"OAuth error: {e}")
        flash('Erreur de connexion avec Google. Veuillez réessayer.', 'error')
        return redirect(url_for('login'))

@app.route('/google', endpoint='google')
def google_callback():
    google = oauth.create_client('google')
    redirect_uri = url_for('authorize', _external=True)
    return google.authorize_redirect(redirect_uri)

# Security headers
@app.after_request
def add_security_headers(response):
    """Add security headers to all responses."""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    response.headers['Content-Security-Policy'] = "default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline'"
    return response

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

@app.route('/')
def index():
    if current_user.is_authenticated:
        print(current_user.data())
        if current_user.is_teacher():
            return redirect(url_for('teacher_home'))
        else:
            return redirect(url_for('home'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        if current_user.is_teacher():
            return redirect(url_for('teacher_home'))
        else:
            return redirect(url_for('home'))

    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')

        user = User.query.filter_by(email=email).first()
        if user and user.check_password(password):
            login_user(user)
            if user.is_teacher():
                return redirect(url_for('teacher_home'))
            else:
                return redirect(url_for('home'))
        else:
            flash('Email ou mot de passe invalide.', 'error')
    return render_template('login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    if request.method == 'POST':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        class_id = int(request.form.get('class_id', 0))
        subject_id = int(request.form.get('subject_id', 0))

        Class = classes.get(class_id, "")
        section = "" if class_id == 1 else subjects.get(subject_id, "")

        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        print(Class,section)
        if not first_name or not last_name or not email or not password or not confirm_password:
            flash('Veuillez remplir tous les champs obligatoires.', 'error')
            return render_template('signup.html')

        if password != confirm_password:
            flash('Les mots de passe ne correspondent pas.', 'error')
            return render_template('signup.html')

        if User.query.filter_by(email=email).first():
            flash('Cet email existe déjà.', 'error')
            return render_template('signup.html')

        user = Student(
            first_name=first_name,
            last_name=last_name,
            email=email,
            Class=Class,
            section=section
        )
        user.set_password(password)
        db.session.add(user)
        db.session.commit()

        login_user(user)
        flash('Compte créé avec succès!', 'success')
        return redirect(url_for('home'))

    return render_template('signup.html')


@app.route('/home')
@login_required
def home():
    return render_template('home.html', user=current_user)

@app.route('/teacher/home')
@login_required
def teacher_home():
    # Define classes with subject restrictions
    return render_template('teacher/teacher_home.html', 
                        user=current_user)
@app.route('/logout', methods=['POST'])
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))


# Admin Routes
@app.route('/admin/login', methods=['GET', 'POST'])
@login_required
def admin_login():
    """Secure admin login with additional verification."""
    if not current_user.can_access_admin():
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('home'))

    if request.method == 'POST':
        # Rate limiting for admin login attempts
        rate_key = f"admin_login:{request.remote_addr}"
        if not rate_limit_check(rate_key, limit=3, window=300):
            flash('Trop de tentatives. Veuillez réessayer dans 5 minutes.', 'error')
            return render_template('admin/admin_login.html')

        # Verify CSRF token
        csrf_token = request.form.get('csrf_token')
        if not verify_csrf_token(csrf_token):
            flash('Token de sécurité invalide.', 'error')
            return render_template('admin/admin_login.html')

        # Additional password verification for admin access
        admin_password = request.form.get('admin_password')
        if admin_password and current_user.check_password(admin_password):
            if create_admin_session():
                flash('Accès administrateur accordé.', 'success')
                return redirect(url_for('admin_dashboard'))

        flash('Mot de passe administrateur incorrect.', 'error')

    return render_template('admin/admin_login.html', csrf_token=generate_csrf_token())


@app.route('/admin/')
def admin_dashboard():
    """Secure admin dashboard."""
    # Get statistics
    total_users = User.query.count()
    total_teachers = Teacher.query.count()
    total_admins = Admin.query.count()
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    print(total_users, total_teachers, total_admins, recent_users)
    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_teachers=total_teachers,
                         total_admins=total_admins,
                         recent_users=recent_users,
                         csrf_token=generate_csrf_token())


@app.route('/admin/logout', methods=['POST'])
@admin_required
def admin_logout():
    """Logout from admin session."""
    destroy_admin_session()
    flash('Déconnexion administrateur réussie.', 'success')
    return redirect(url_for('home'))


# Teacher Management Routes
@app.route('/admin/teachers')
@admin_required
def admin_teachers():
    """View all teachers."""
    teachers = Teacher.query.order_by(Teacher.created_at.desc()).all()
    return render_template('admin/teachers.html',
                         teachers=teachers,
                         csrf_token=generate_csrf_token())


@app.route('/admin/teachers/add', methods=['GET', 'POST'])
@admin_required
def admin_add_teacher():
    """Add a new teacher."""
    if request.method == 'POST':
        # Verify CSRF token
        csrf_token = request.form.get('csrf_token')
        if not verify_csrf_token(csrf_token):
            flash('Token de sécurité invalide.', 'error')
            return render_template('admin/add_teacher.html', csrf_token=generate_csrf_token())

        # Get form data
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')

        # Validation
        errors = []
        if not first_name:
            errors.append('Le prénom est requis.')
        if not last_name:
            errors.append('Le nom de famille est requis.')
        if not email:
            errors.append('L\'email est requis.')
        if not password:
            errors.append('Le mot de passe est requis.')
        if password != confirm_password:
            errors.append('Les mots de passe ne correspondent pas.')
        if len(password) < 8:
            errors.append('Le mot de passe doit contenir au moins 8 caractères.')

        # Check if email already exists
        if User.query.filter_by(email=email).first():
            errors.append('Cet email existe déjà.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('admin/add_teacher.html',
                                 csrf_token=generate_csrf_token(),
                                 first_name=first_name,
                                 last_name=last_name,
                                 email=email)

        # Create teacher
        teacher = Teacher(
            first_name=first_name,
            last_name=last_name,
            email=email
        )
        teacher.set_password(password)

        try:
            db.session.add(teacher)
            db.session.commit()
            flash(f'Enseignant {teacher.name} ajouté avec succès.', 'success')
            return redirect(url_for('admin_teachers'))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de l\'ajout de l\'enseignant.', 'error')

    return render_template('admin/add_teacher.html', csrf_token=generate_csrf_token())


@app.route('/admin/teachers/<int:teacher_id>/edit', methods=['GET', 'POST'])
@admin_required
def admin_edit_teacher(teacher_id):
    """Edit a teacher."""
    teacher = Teacher.query.get_or_404(teacher_id)

    if request.method == 'POST':
        # Verify CSRF token
        csrf_token = request.form.get('csrf_token')
        if not verify_csrf_token(csrf_token):
            flash('Token de sécurité invalide.', 'error')
            return render_template('admin/edit_teacher.html',
                                 teacher=teacher,
                                 csrf_token=generate_csrf_token())

        # Get form data
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        email = request.form.get('email', '').strip().lower()
        is_active = request.form.get('is_active') == 'on'
        new_password = request.form.get('new_password', '')

        # Validation
        errors = []
        if not first_name:
            errors.append('Le prénom est requis.')
        if not last_name:
            errors.append('Le nom de famille est requis.')
        if not email:
            errors.append('L\'email est requis.')

        # Check if email already exists (excluding current teacher)
        existing_user = User.query.filter_by(email=email).first()
        if existing_user and existing_user.id != teacher.id:
            errors.append('Cet email existe déjà.')

        if new_password and len(new_password) < 8:
            errors.append('Le nouveau mot de passe doit contenir au moins 8 caractères.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('admin/edit_teacher.html',
                                 teacher=teacher,
                                 csrf_token=generate_csrf_token())

        # Update teacher
        teacher.first_name = first_name
        teacher.last_name = last_name
        teacher.email = email
        teacher.is_active = is_active

        if new_password:
            teacher.set_password(new_password)

        try:
            db.session.commit()
            flash(f'Enseignant {teacher.name} modifié avec succès.', 'success')
            return redirect(url_for('admin_teachers'))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la modification de l\'enseignant.', 'error')

    return render_template('admin/edit_teacher.html',
                         teacher=teacher,
                         csrf_token=generate_csrf_token())


@app.route('/admin/teachers/<int:teacher_id>/delete', methods=['POST'])
@admin_required
def admin_delete_teacher(teacher_id):
    """Delete a teacher."""
    teacher = Teacher.query.get_or_404(teacher_id)

    # Verify CSRF token
    csrf_token = request.form.get('csrf_token')
    if not verify_csrf_token(csrf_token):
        flash('Token de sécurité invalide.', 'error')
        return redirect(url_for('admin_teachers'))

    try:
        teacher_name = teacher.name
        db.session.delete(teacher)
        db.session.commit()
        flash(f'Enseignant {teacher_name} supprimé avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression de l\'enseignant.', 'error')

    return redirect(url_for('admin_teachers'))



if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
