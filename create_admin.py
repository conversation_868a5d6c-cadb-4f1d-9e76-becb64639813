"""
<PERSON><PERSON><PERSON> to create the first admin user for the Flask application.
Run this script once to set up your admin account.
"""
import os
import sys
from getpass import getpass

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, Admin , User


def create_admin_user():
    """Create the first admin user."""
    print("🛡️  Configuration de l'Administrateur Principal")
    print("=" * 50)
    
    with app.app_context():
        # Check if admin already exists
        existing_admin = Admin.query.first()
        if existing_admin:
            print(f"⚠️  Un administrateur existe déjà : {existing_admin.email}")
            response = input("Voulez-vous créer un autre administrateur ? (o/N): ").lower()
            if response not in ['o', 'oui', 'y', 'yes']:
                print("Opération annulée.")
                return
        
        print("\n📝 Veuillez entrer les informations de l'administrateur :")
        
        # Get admin details
        while True:
            first_name = input("Prénom : ").strip()
            if first_name:
                break
            print("❌ Le prénom est requis.")
        
        while True:
            last_name = input("Nom de famille : ").strip()
            if last_name:
                break
            print("❌ Le nom de famille est requis.")
        
        while True:
            email = input("Email : ").strip().lower()
            if email and '@' in email:
                # Check if email already exists in any user table
                if User.query.filter_by(email=email).first():
                    print("❌ Cet email existe déjà.")
                    continue
                break
            print("❌ Veuillez entrer un email valide.")
        
        while True:
            password = getpass("Mot de passe (minimum 8 caractères) : ")
            if len(password) >= 8:
                confirm_password = getpass("Confirmer le mot de passe : ")
                if password == confirm_password:
                    break
                else:
                    print("❌ Les mots de passe ne correspondent pas.")
            else:
                print("❌ Le mot de passe doit contenir au moins 8 caractères.")

        
        # Create admin user
        admin_user = Admin(
            first_name=first_name,
            last_name=last_name,
            email=email,
        )
        admin_user.set_password(password)
        
        try:
            db.session.add(admin_user)
            db.session.commit()
            
            print("\n✅ Administrateur créé avec succès !")
            print(f"📧 Email : {email}")
            print(f"👤 Nom : {admin_user.get_full_name()}")
            print(f"🛡️  Rôle : Administrateur")
            print("\n🔐 Vous pouvez maintenant vous connecter et accéder à l'administration.")
            print("📍 URL d'administration : /admin/login")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur lors de la création de l'administrateur : {e}")
            return False
        
        return True

def main():
    """Main function."""
    print("🚀 Initialisation de l'Application Flask")
    print("=" * 50)
    
    try:
        with app.app_context():
            # Create tables if they don't exist
            db.create_all()
            print("✅ Base de données initialisée.")
            
            # Create admin user
            if create_admin_user():
                print("\n🎉 Configuration terminée avec succès !")
                print("\n📋 Prochaines étapes :")
                print("1. Démarrez l'application : python app.py")
                print("2. Connectez-vous avec votre compte")
                print("3. Cliquez sur 'Administration' sur la page d'accueil")
                print("4. Entrez votre mot de passe pour accéder à l'admin")
                print("5. Commencez à ajouter des enseignants !")
            else:
                print("\n❌ Échec de la configuration.")
                
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return False
    
    return True


if __name__ == '__main__':
    main()
