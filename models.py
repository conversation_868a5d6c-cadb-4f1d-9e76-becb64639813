"""
Database models for the Flask application.
"""
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone

db = SQLAlchemy()

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(100), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # Discriminator column for inheritance
    user_type = db.Column(db.String(20), nullable=False, default='user')
    
    __mapper_args__ = {
        'polymorphic_identity': 'user',
        'polymorphic_on': user_type,
        'with_polymorphic': '*'
    }
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def name(self):
        """Get full name."""
        return f"{self.first_name} {self.last_name}"

    def set_password(self, password):
        """Set password hash."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash."""
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return isinstance(self, Admin) and self.is_active

    def is_teacher(self):
        return isinstance(self, Teacher) and self.is_active

    def can_access_admin(self):
        return self.is_admin() and self.is_active
    def get_user_type(self):
        """Get user type as string."""
        if isinstance(self, Teacher):
            return 'teacher'
        elif isinstance(self, Admin):
            return 'admin'
        elif isinstance(self, Student):
            return 'student'
        else:
            return 'user'
    
    def get_role_display(self):
        """Get human-readable role name."""
        role_names = {
            'teacher': 'Enseignant',
            'admin': 'Administrateur',
            'student': 'Étudiant',
        }
        return role_names.get(self.get_user_type(), 'Utilisateur')
    def data(self):
        return {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'created_at': self.created_at,
            'is_active': self.is_active,
            'role' : self.get_user_type(),
        }
    
class Student(User):
    """Student user with academic information."""
    __tablename__ = 'students'
    
    id = db.Column(db.Integer, db.ForeignKey('users.id'), primary_key=True)
    Class = db.Column(db.String(50), nullable=False)
    section = db.Column(db.String(50), nullable=False)
    __mapper_args__ = {
        'polymorphic_identity': 'student',
    }

class Teacher(User):
    """Teacher user with educational background."""
    __tablename__ = 'teachers'
    
    id = db.Column(db.Integer, db.ForeignKey('users.id'), primary_key=True)

    __mapper_args__ = {
        'polymorphic_identity': 'teacher',
    }

class Admin(User):
    """Administrator user with system privileges."""
    __tablename__ = 'admins'
    
    id = db.Column(db.Integer, db.ForeignKey('users.id'), primary_key=True)
    
    __mapper_args__ = {
        'polymorphic_identity': 'admin',
    }