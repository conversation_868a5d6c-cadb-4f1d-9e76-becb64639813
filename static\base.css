/* Modern Professional Design System */
:root {
    /* Primary Colors - Professional Blue Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Semantic Colors */
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;

    /* Typography */
    --font-family-sans: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;

    /* Font Weights */
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-normal: 200ms ease;
    --transition-slow: 300ms ease;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: 1.6;
    color: var(--gray-900);
    background-color: var(--gray-50);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Remove link underlines globally */
a {
    color: var(--primary-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-700);
}

a:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-semibold);
    line-height: 1.25;
    color: var(--gray-900);
}

h1 {
    font-size: var(--text-3xl);
    margin-bottom: var(--space-6);
}

h2 {
    font-size: var(--text-2xl);
    margin-bottom: var(--space-5);
}

h3 {
    font-size: var(--text-xl);
    margin-bottom: var(--space-4);
}

h4 {
    font-size: var(--text-lg);
    margin-bottom: var(--space-3);
}

p {
    margin-bottom: var(--space-4);
    color: var(--gray-700);
}

/* Professional Flash Messages */
.flash-messages {
    position: fixed;
    top: var(--space-4);
    right: var(--space-4);
    z-index: 1000;
    max-width: 400px;
    width: 100%;
}

.flash-message {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    margin-bottom: var(--space-3);
    border-radius: var(--radius-lg);
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    box-shadow: var(--shadow-lg);
    animation: slideIn 0.3s ease;
    border-left: 4px solid;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.flash-message.error {
    background-color: var(--error-50);
    color: var(--error-700);
    border-left-color: var(--error-500);
}

.flash-message.success {
    background-color: var(--success-50);
    color: var(--success-700);
    border-left-color: var(--success-500);
}

.flash-message.info {
    background-color: var(--primary-50);
    color: var(--primary-700);
    border-left-color: var(--primary-500);
}

.flash-message.warning {
    background-color: var(--warning-50);
    color: var(--warning-600);
    border-left-color: var(--warning-500);
}

/* Professional Container System */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

/* Professional Form System */
.form-container {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.form-header {
    padding: var(--space-8) var(--space-8) var(--space-6);
    text-align: center;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
}

.form-header h1 {
    color: white;
    margin-bottom: var(--space-2);
    font-size: var(--text-2xl);
}

.form-header p {
    color: var(--primary-100);
    margin-bottom: 0;
    font-size: var(--text-sm);
}

.form-body {
    padding: var(--space-8);
}

.form-group {
    margin-bottom: var(--space-6);
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: var(--font-medium);
    color: var(--gray-700);
    font-size: var(--text-sm);
}

.form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-family: inherit;
    background-color: white;
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
    background-color: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

.form-input.error {
    border-color: var(--error-500);
}

.form-input.error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-select {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-family: inherit;
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-2) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: var(--space-10);
    transition: all var(--transition-fast);
    appearance: none;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-family: inherit;
    background-color: white;
    transition: all var(--transition-fast);
    resize: vertical;
    min-height: 100px;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-error {
    margin-top: var(--space-1);
    font-size: var(--text-xs);
    color: var(--error-600);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.form-help {
    margin-top: var(--space-1);
    font-size: var(--text-xs);
    color: var(--gray-500);
}

/* Professional Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    font-family: inherit;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
    background-color: var(--primary-600);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-700);
    color: white;
}

.btn-secondary {
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.btn-success {
    background-color: var(--success-600);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: var(--success-700);
    color: white;
}

.btn-danger {
    background-color: var(--error-600);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: var(--error-700);
    color: white;
}

.btn-outline {
    background-color: transparent;
    border-color: var(--primary-600);
    color: var(--primary-600);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--primary-600);
    color: white;
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
}

.btn-full {
    width: 100%;
}

/* Professional Card System */
.card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-primary {
    color: var(--primary-600);
}

.text-secondary {
    color: var(--gray-600);
}

.text-success {
    color: var(--success-600);
}

.text-warning {
    color: var(--warning-600);
}

.text-danger {
    color: var(--error-600);
}

.text-muted {
    color: var(--gray-500);
}

.bg-primary {
    background-color: var(--primary-600);
    color: white;
}

.bg-secondary {
    background-color: var(--gray-100);
}

.bg-success {
    background-color: var(--success-600);
    color: white;
}

.bg-warning {
    background-color: var(--warning-600);
    color: white;
}

.bg-danger {
    background-color: var(--error-600);
    color: white;
}

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

/* Responsive Design */
@media (max-width: 640px) {
    .container {
        padding: 0 var(--space-3);
    }

    .form-body {
        padding: var(--space-6) var(--space-4);
    }

    .form-header {
        padding: var(--space-6) var(--space-4) var(--space-4);
    }

    .card-header,
    .card-body,
    .card-footer {
        padding: var(--space-4);
    }

    .btn {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-sm);
    }

    .btn-lg {
        padding: var(--space-4) var(--space-6);
    }

    h1 {
        font-size: var(--text-2xl);
    }

    h2 {
        font-size: var(--text-xl);
    }

    .flash-messages {
        top: var(--space-2);
        right: var(--space-2);
        left: var(--space-2);
        max-width: none;
    }
}

@media (max-width: 480px) {
    .form-header h1 {
        font-size: var(--text-xl);
    }

    .btn {
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-xs);
    }

    .form-input,
    .form-select,
    .form-textarea {
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-sm);
    }
}

/* Focus and Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Print Styles */
@media print {
    .flash-messages,
    .btn,
    button {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }

    body {
        background: white;
    }
}
