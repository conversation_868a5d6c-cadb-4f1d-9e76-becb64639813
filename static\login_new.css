/* Professional Authentication Page Styles */
.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: var(--space-4);
    position: relative;
}

.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    pointer-events: none;
}

.auth-container {
    width: 100%;
    max-width: 420px;
    position: relative;
    z-index: 1;
}

/* Enhanced Form Container */
.form-container {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Professional Header */
.form-header {
    background: var(--gradient-primary);
    color: white;
    text-align: center;
    padding: var(--space-10) var(--space-8) var(--space-8);
    position: relative;
}

.form-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.logo {
    width: 4rem;
    height: 4rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
    font-size: 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.form-header h1 {
    color: white;
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-2);
}

.form-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--text-sm);
    margin: 0;
}

/* Enhanced Form Body */
.form-body {
    padding: var(--space-8);
}

.auth-form {
    margin-bottom: var(--space-6);
}

/* Enhanced Form Groups */
.form-group {
    margin-bottom: var(--space-6);
    position: relative;
}

.form-group.focused .form-label {
    color: var(--primary-600);
}

.form-group.focused .form-label i {
    color: var(--primary-600);
}

.form-label {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
    font-weight: var(--font-medium);
    color: var(--gray-700);
    font-size: var(--text-sm);
    transition: color var(--transition-fast);
}

.form-label i {
    color: var(--gray-400);
    font-size: var(--text-sm);
    transition: color var(--transition-fast);
}

/* Enhanced Password Input */
.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: var(--gray-600);
    background: var(--gray-100);
}

.password-toggle:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Professional Divider */
.auth-divider {
    position: relative;
    text-align: center;
    margin: var(--space-6) 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-200);
}

.auth-divider span {
    background: white;
    padding: 0 var(--space-4);
    color: var(--gray-500);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
}

/* Enhanced Google Button */
.google-auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
    width: 100%;
    padding: var(--space-4) var(--space-6);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: white;
    color: var(--gray-700);
    text-decoration: none;
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.google-auth-btn:hover {
    border-color: var(--primary-300);
    background: var(--gray-50);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--gray-800);
}

.google-icon {
    width: 1.25rem;
    height: 1.25rem;
    flex-shrink: 0;
}

/* Ripple Effect */
.google-auth-btn .ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.3);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Professional Footer */
.auth-footer {
    text-align: center;
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    border-top: 1px solid var(--gray-200);
}

.auth-footer p {
    color: var(--gray-600);
    font-size: var(--text-sm);
    margin-bottom: var(--space-3);
}

.auth-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--primary-600);
    text-decoration: none;
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    transition: all var(--transition-fast);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
}

.auth-link:hover {
    color: var(--primary-700);
    background: var(--primary-50);
    transform: translateX(2px);
}

.auth-link i {
    font-size: var(--text-xs);
    transition: transform var(--transition-fast);
}

.auth-link:hover i {
    transform: translateX(2px);
}

/* Responsive Design */
@media (max-width: 640px) {
    .auth-page {
        padding: var(--space-3);
    }
    
    .form-header {
        padding: var(--space-8) var(--space-6) var(--space-6);
    }
    
    .form-body {
        padding: var(--space-6) var(--space-4);
    }
    
    .logo {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }
    
    .form-header h1 {
        font-size: var(--text-xl);
    }
}

@media (max-width: 480px) {
    .auth-container {
        max-width: 100%;
    }
    
    .form-header {
        padding: var(--space-6) var(--space-4) var(--space-4);
    }
    
    .form-body {
        padding: var(--space-4);
    }
    
    .form-header h1 {
        font-size: var(--text-lg);
    }
    
    .google-auth-btn {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-xs);
    }
    
    .google-icon {
        width: 1rem;
        height: 1rem;
    }
}
